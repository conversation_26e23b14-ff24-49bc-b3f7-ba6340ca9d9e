# 金刚位配置管理API接口设计

## 1. 数据库设计（极简架构）

### 1.1 金刚位表结构
```sql
CREATE TABLE `oto_diamond_position` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '金刚位名称',
  `icon` text DEFAULT NULL COMMENT '图标内容（URL、SVG代码、Base64等）',
  `icon_type` varchar(20) DEFAULT 'url' COMMENT '图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号',
  `url` varchar(500) DEFAULT NULL COMMENT '跳转链接',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `create_dept` bigint COMMENT '创建部门',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_icon_type` (`icon_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='金刚位配置表';
```

### 1.2 图标类型设计

#### 支持的图标类型

| 类型 | 值 | 描述 | 示例 | 前端处理方式 |
|------|-----|------|------|-------------|
| URL | url | 网络链接或本地路径 | `https://example.com/icon.png` | `<img src={icon} />` |
| SVG | svg | SVG矢量图代码 | `<svg viewBox="0 0 24 24">...</svg>` | `dangerouslySetInnerHTML` |
| Base64 | base64 | Base64编码图片 | `data:image/png;base64,iVBORw0...` | `<img src={icon} />` |
| 表情符号 | emoji | Unicode表情符号 | `💰` `🔧` `💳` | `<span>{icon}</span>` |

#### 图标类型验证规则

1. **URL类型**: 必须以 `http://`, `https://` 或 `/` 开头
2. **SVG类型**: 必须以 `<svg` 开头，以 `</svg>` 结尾
3. **Base64类型**: 必须以 `data:image/` 开头
4. **表情符号类型**: 长度不超过10个字符

### 1.3 文件上传接口设计

#### 图片上传接口
```http
POST /api/diamond-position/upload/image
Content-Type: multipart/form-data

参数：
- file: 图片文件（PNG、JPG、JPEG、GIF、WebP）
- type: 上传类型（url | base64）

响应：
{
  "success": true,
  "data": {
    "url": "https://example.com/uploads/icon_123456.png",  // type=url时返回
    "base64": "data:image/png;base64,iVBORw0...",          // type=base64时返回
    "originalName": "icon.png",
    "size": 12345,
    "mimeType": "image/png"
  },
  "message": "上传成功"
}
```

#### SVG文件上传接口
```http
POST /api/diamond-position/upload/svg
Content-Type: multipart/form-data

参数：
- file: SVG文件

响应：
{
  "success": true,
  "data": {
    "svgContent": "<svg viewBox=\"0 0 24 24\">...</svg>",
    "originalName": "icon.svg",
    "size": 1234
  },
  "message": "SVG文件读取成功"
}
```

## 2. 后端API接口设计

### 2.1 Controller层
```java
@RestController
@RequestMapping("/api/diamond-position")
@Api(tags = "金刚位配置管理")
public class DiamondPositionController {

    @Autowired
    private DiamondPositionService diamondPositionService;

    /**
     * 分页查询金刚位列表
     */
    @GetMapping("/page")
    @ApiOperation("分页查询金刚位列表")
    public Result<PageResult<DiamondPositionVO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer status) {
        
        DiamondPositionQueryDTO queryDTO = new DiamondPositionQueryDTO();
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        queryDTO.setName(name);
        queryDTO.setStatus(status);
        
        return Result.success(diamondPositionService.page(queryDTO));
    }

    /**
     * 获取所有启用的金刚位（用于前端展示）
     */
    @GetMapping("/list")
    @ApiOperation("获取所有启用的金刚位")
    public Result<List<DiamondPositionVO>> list() {
        return Result.success(diamondPositionService.listEnabled());
    }

    /**
     * 根据ID获取金刚位详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取金刚位详情")
    public Result<DiamondPositionVO> getById(@PathVariable Long id) {
        return Result.success(diamondPositionService.getById(id));
    }

    /**
     * 新增金刚位
     */
    @PostMapping
    @ApiOperation("新增金刚位")
    public Result<Void> add(@RequestBody @Valid DiamondPositionAddDTO addDTO) {
        diamondPositionService.add(addDTO);
        return Result.success();
    }

    /**
     * 更新金刚位
     */
    @PutMapping("/{id}")
    @ApiOperation("更新金刚位")
    public Result<Void> update(@PathVariable Long id, 
                              @RequestBody @Valid DiamondPositionUpdateDTO updateDTO) {
        updateDTO.setId(id);
        diamondPositionService.update(updateDTO);
        return Result.success();
    }

    /**
     * 删除金刚位
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除金刚位")
    public Result<Void> delete(@PathVariable Long id) {
        diamondPositionService.delete(id);
        return Result.success();
    }

    /**
     * 批量删除金刚位
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除金刚位")
    public Result<Void> batchDelete(@RequestBody List<Long> ids) {
        diamondPositionService.batchDelete(ids);
        return Result.success();
    }

    /**
     * 更新金刚位排序
     */
    @PutMapping("/sort")
    @ApiOperation("更新金刚位排序")
    public Result<Void> updateSort(@RequestBody List<DiamondPositionSortDTO> sortList) {
        diamondPositionService.updateSort(sortList);
        return Result.success();
    }

    /**
     * 启用/禁用金刚位
     */
    @PutMapping("/{id}/status")
    @ApiOperation("启用/禁用金刚位")
    public Result<Void> updateStatus(@PathVariable Long id, 
                                    @RequestParam Integer status) {
        diamondPositionService.updateStatus(id, status);
        return Result.success();
    }
}
```

### 2.2 DTO类设计
```java
// 查询DTO
@Data
public class DiamondPositionQueryDTO {
    private Integer pageNum;
    private Integer pageSize;
    private String name;
    private Integer status;
}

// 新增DTO
@Data
public class DiamondPositionAddDTO {
    @NotBlank(message = "金刚位名称不能为空")
    @Length(max = 100, message = "金刚位名称长度不能超过100个字符")
    private String name;
    
    private String icon;
    
    @URL(message = "链接格式不正确")
    private String url;
    
    @Min(value = 0, message = "排序序号不能小于0")
    private Integer sortOrder;
    
    @Range(min = 0, max = 1, message = "状态值只能是0或1")
    private Integer status;
    
    @Length(max = 500, message = "描述长度不能超过500个字符")
    private String description;
}

// 更新DTO
@Data
public class DiamondPositionUpdateDTO {
    private Long id;
    
    @NotBlank(message = "金刚位名称不能为空")
    @Length(max = 100, message = "金刚位名称长度不能超过100个字符")
    private String name;
    
    private String icon;
    
    @URL(message = "链接格式不正确")
    private String url;
    
    @Min(value = 0, message = "排序序号不能小于0")
    private Integer sortOrder;
    
    @Range(min = 0, max = 1, message = "状态值只能是0或1")
    private Integer status;
    
    @Length(max = 500, message = "描述长度不能超过500个字符")
    private String description;
}

// 排序DTO
@Data
public class DiamondPositionSortDTO {
    @NotNull(message = "ID不能为空")
    private Long id;
    
    @NotNull(message = "排序序号不能为空")
    @Min(value = 0, message = "排序序号不能小于0")
    private Integer sortOrder;
}

// 返回VO
@Data
public class DiamondPositionVO {
    private Long id;
    private String name;
    private String icon;
    private String url;
    private Integer sortOrder;
    private Integer status;
    private String description;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String createdBy;
    private String updatedBy;
}
```

### 2.3 Service层接口
```java
public interface DiamondPositionService {
    
    /**
     * 分页查询金刚位列表
     */
    PageResult<DiamondPositionVO> page(DiamondPositionQueryDTO queryDTO);
    
    /**
     * 获取所有启用的金刚位
     */
    List<DiamondPositionVO> listEnabled();
    
    /**
     * 根据ID获取金刚位详情
     */
    DiamondPositionVO getById(Long id);
    
    /**
     * 新增金刚位
     */
    void add(DiamondPositionAddDTO addDTO);
    
    /**
     * 更新金刚位
     */
    void update(DiamondPositionUpdateDTO updateDTO);
    
    /**
     * 删除金刚位
     */
    void delete(Long id);
    
    /**
     * 批量删除金刚位
     */
    void batchDelete(List<Long> ids);
    
    /**
     * 更新金刚位排序
     */
    void updateSort(List<DiamondPositionSortDTO> sortList);
    
    /**
     * 启用/禁用金刚位
     */
    void updateStatus(Long id, Integer status);
}
```

## 3. 前端API调用接口

### 3.1 API定义文件
```typescript
// api/diamond-position.ts
import { http } from '@/utils/http'
import type { 
  DiamondPositionVO, 
  DiamondPositionQueryDTO, 
  DiamondPositionAddDTO, 
  DiamondPositionUpdateDTO,
  DiamondPositionSortDTO,
  PageResult 
} from '@/types/diamond-position'

/**
 * 金刚位配置管理API
 */
export const diamondPositionApi = {
  /**
   * 分页查询金刚位列表
   */
  page: (params: DiamondPositionQueryDTO) => {
    return http.get<PageResult<DiamondPositionVO>>('/api/diamond-position/page', { params })
  },

  /**
   * 获取所有启用的金刚位
   */
  list: () => {
    return http.get<DiamondPositionVO[]>('/api/diamond-position/list')
  },

  /**
   * 根据ID获取金刚位详情
   */
  getById: (id: number) => {
    return http.get<DiamondPositionVO>(`/api/diamond-position/${id}`)
  },

  /**
   * 新增金刚位
   */
  add: (data: DiamondPositionAddDTO) => {
    return http.post('/api/diamond-position', data)
  },

  /**
   * 更新金刚位
   */
  update: (id: number, data: DiamondPositionUpdateDTO) => {
    return http.put(`/api/diamond-position/${id}`, data)
  },

  /**
   * 删除金刚位
   */
  delete: (id: number) => {
    return http.delete(`/api/diamond-position/${id}`)
  },

  /**
   * 批量删除金刚位
   */
  batchDelete: (ids: number[]) => {
    return http.delete('/api/diamond-position/batch', { data: ids })
  },

  /**
   * 更新金刚位排序
   */
  updateSort: (data: DiamondPositionSortDTO[]) => {
    return http.put('/api/diamond-position/sort', data)
  },

  /**
   * 启用/禁用金刚位
   */
  updateStatus: (id: number, status: number) => {
    return http.put(`/api/diamond-position/${id}/status`, null, { 
      params: { status } 
    })
  }
}
```

### 3.2 TypeScript类型定义
```typescript
// types/diamond-position.ts
export interface DiamondPositionVO {
  id: number
  name: string
  icon?: string
  url?: string
  sortOrder: number
  status: number
  description?: string
  createdTime: string
  updatedTime: string
  createdBy?: string
  updatedBy?: string
}

export interface DiamondPositionQueryDTO {
  pageNum?: number
  pageSize?: number
  name?: string
  status?: number
}

export interface DiamondPositionAddDTO {
  name: string
  icon?: string
  url?: string
  sortOrder?: number
  status?: number
  description?: string
}

export interface DiamondPositionUpdateDTO extends DiamondPositionAddDTO {
  id?: number
}

export interface DiamondPositionSortDTO {
  id: number
  sortOrder: number
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}
```

## 4. 响应式布局CSS Grid方案

### 4.1 CSS Grid响应式布局
```css
/* 金刚位网格布局 */
.diamond-grid {
  display: grid;
  gap: 16px;
  padding: 16px;
  
  /* 手机端：2列 */
  grid-template-columns: repeat(2, 1fr);
}

/* 平板端：4列 */
@media (min-width: 768px) {
  .diamond-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px;
  }
}

/* 桌面端：6列 */
@media (min-width: 1024px) {
  .diamond-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 24px;
    padding: 24px;
  }
}

/* 大屏幕：8列 */
@media (min-width: 1440px) {
  .diamond-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

/* 金刚位单项样式 */
.diamond-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.diamond-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.diamond-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
  border-radius: 8px;
}

.diamond-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* 响应式图标大小 */
@media (min-width: 768px) {
  .diamond-icon {
    width: 48px;
    height: 48px;
  }
  
  .diamond-name {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  .diamond-icon {
    width: 56px;
    height: 56px;
  }
}
```

## 5. 技术特点总结

### 5.1 极简架构优势
- **简单高效**：单表设计，减少复杂的关联查询
- **易于维护**：数据结构清晰，业务逻辑简单
- **性能优秀**：减少数据库查询次数，提高响应速度
- **扩展性好**：后续可根据业务需要灵活扩展

### 5.2 响应式设计优势
- **自适应布局**：CSS Grid自动适配不同屏幕尺寸
- **用户体验佳**：在各种设备上都有良好的展示效果
- **维护成本低**：一套代码适配多端，减少开发工作量

### 5.3 API设计优势
- **RESTful规范**：遵循标准的REST API设计原则
- **类型安全**：TypeScript提供完整的类型定义
- **功能完整**：涵盖增删改查、排序、状态管理等所有功能
- **易于测试**：接口设计清晰，便于单元测试和集成测试

这套方案完全满足金刚位配置管理的所有需求，同时保持了架构的简洁性和可维护性。