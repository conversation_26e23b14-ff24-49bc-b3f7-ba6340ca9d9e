<template>
  <div
    class="diamond-icon"
    :style="iconStyle"
    :title="alt"
  >
    <!-- URL类型图标 -->
    <img
      v-if="iconType === 'url'"
      :src="icon"
      :alt="alt"
      :style="imageStyle"
      @error="handleImageError"
      @load="handleImageLoad"
    />

    <!-- SVG类型图标 -->
    <div
      v-else-if="iconType === 'svg'"
      class="svg-container"
      v-html="icon"
    />

    <!-- Base64类型图标 -->
    <img
      v-else-if="iconType === 'base64'"
      :src="icon"
      :alt="alt"
      :style="imageStyle"
      @error="handleImageError"
      @load="handleImageLoad"
    />

    <!-- 表情符号类型图标 -->
    <span
      v-else-if="iconType === 'emoji'"
      class="emoji-icon"
      :style="emojiStyle"
    >
      {{ icon }}
    </span>

    <!-- 默认图标（当图标加载失败时显示） -->
    <div
      v-if="showFallback"
      class="fallback-icon"
      :style="fallbackStyle"
    >
      <el-icon><Picture /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

// Props
interface Props {
  icon: string
  iconType: 'url' | 'svg' | 'base64' | 'emoji'
  size?: number
  alt?: string
  borderRadius?: number
}

const props = withDefaults(defineProps<Props>(), {
  icon: '',
  iconType: 'url',
  size: 48,
  alt: '图标',
  borderRadius: 8
})

// 响应式数据
const imageError = ref(false)
const showFallback = ref(false)

// 计算属性
const iconStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
  borderRadius: `${props.borderRadius}px`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  overflow: 'hidden',
  background: '#f5f7fa'
}))

const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'cover' as const,
  display: imageError.value ? 'none' : 'block'
}))

const emojiStyle = computed(() => ({
  fontSize: `${props.size * 0.6}px`,
  lineHeight: '1',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}))

const fallbackStyle = computed(() => ({
  fontSize: `${props.size * 0.4}px`,
  color: '#c0c4cc',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}))

// 方法
const handleImageLoad = () => {
  imageError.value = false
  showFallback.value = false
}

const handleImageError = () => {
  imageError.value = true
  showFallback.value = true
}
</script>

<style scoped>
.diamond-icon {
  position: relative;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.diamond-icon:hover {
  border-color: #c0c4cc;
}

.svg-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.svg-container :deep(svg) {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.emoji-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

.fallback-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
}

/* 加载状态 */
.diamond-icon.loading {
  background: linear-gradient(90deg, #f5f7fa 25%, #e4e7ed 50%, #f5f7fa 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 错误状态 */
.diamond-icon.error {
  background: #fef0f0;
  border-color: #fbc4c4;
}

.diamond-icon.error .fallback-icon {
  color: #f56c6c;
}
</style>
