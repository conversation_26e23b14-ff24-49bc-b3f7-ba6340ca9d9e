<template>
  <el-dialog
    v-model="dialogVisible"
    title="多设备预览"
    width="90%"
    :close-on-click-modal="false"
    custom-class="device-preview-dialog"
  >
    <div class="preview-container">
      <!-- 设备切换标签 -->
      <div class="device-tabs">
        <el-radio-group v-model="activeDevice" @change="handleDeviceChange">
          <el-radio-button
            v-for="device in devices"
            :key="device.type"
            :label="device.type"
          >
            <el-icon>
              <component :is="device.icon" />
            </el-icon>
            {{ device.name }}
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 预览区域 -->
      <div class="preview-area">
        <div class="device-container">
          <!-- 设备外框 -->
          <div
            class="device-frame"
            :class="[`device-${activeDevice}`, { 'landscape': isLandscape }]"
            :style="deviceFrameStyle"
          >
            <!-- 设备屏幕 -->
            <div class="device-screen" :style="deviceScreenStyle">
              <!-- 状态栏（仅移动设备） -->
              <div
                v-if="activeDevice === 'mobile'"
                class="status-bar"
              >
                <span class="time">9:41</span>
                <div class="status-icons">
                  <span class="signal">●●●</span>
                  <span class="wifi">📶</span>
                  <span class="battery">🔋</span>
                </div>
              </div>

              <!-- 应用头部 -->
              <div class="app-header">
                <div class="header-content">
                  <span class="app-title">应用首页</span>
                </div>
              </div>

              <!-- 金刚位网格 -->
              <div class="diamond-grid-preview" :style="gridStyle">
                <div
                  v-for="item in enabledItems"
                  :key="item.id"
                  class="diamond-item-preview"
                  :style="itemStyle"
                >
                  <div class="item-icon-preview">
                    <DiamondIcon
                      :icon="item.icon"
                      :icon-type="item.iconType"
                      :size="currentConfig.iconSize"
                      :alt="item.name"
                    />
                  </div>
                  <div class="item-name-preview">{{ item.name }}</div>
                </div>

                <!-- 空位占位符 -->
                <div
                  v-for="i in emptySlots"
                  :key="`empty-${i}`"
                  class="diamond-item-preview empty-slot"
                  :style="itemStyle"
                >
                  <div class="empty-icon">
                    <el-icon><Plus /></el-icon>
                  </div>
                </div>
              </div>

              <!-- 底部导航（仅移动设备） -->
              <div
                v-if="activeDevice === 'mobile'"
                class="bottom-navigation"
              >
                <div class="nav-item active">
                  <el-icon><House /></el-icon>
                  <span>首页</span>
                </div>
                <div class="nav-item">
                  <el-icon><Search /></el-icon>
                  <span>搜索</span>
                </div>
                <div class="nav-item">
                  <el-icon><User /></el-icon>
                  <span>我的</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备信息 -->
        <div class="device-info">
          <h3>{{ currentConfig.name }}</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">屏幕尺寸:</span>
              <span class="value">{{ currentConfig.width }} × {{ currentConfig.height }}</span>
            </div>
            <div class="info-item">
              <span class="label">网格列数:</span>
              <span class="value">{{ currentConfig.columns }}</span>
            </div>
            <div class="info-item">
              <span class="label">图标大小:</span>
              <span class="value">{{ currentConfig.iconSize }}px</span>
            </div>
            <div class="info-item">
              <span class="label">间距:</span>
              <span class="value">{{ currentConfig.gap }}px</span>
            </div>
            <div class="info-item">
              <span class="label">金刚位数量:</span>
              <span class="value">{{ enabledItems.length }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="control-panel">
        <div class="control-group">
          <label>方向:</label>
          <el-radio-group v-model="isLandscape">
            <el-radio :label="false">竖屏</el-radio>
            <el-radio :label="true">横屏</el-radio>
          </el-radio-group>
        </div>

        <div class="control-group">
          <label>缩放:</label>
          <el-slider
            v-model="scale"
            :min="0.3"
            :max="1"
            :step="0.1"
            style="width: 200px"
          />
          <span class="scale-value">{{ Math.round(scale * 100) }}%</span>
        </div>

        <div class="control-group">
          <el-button @click="resetPreview">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="exportPreview">
            <el-icon><Download /></el-icon>
            导出截图
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Plus,
  House,
  Search,
  User,
  Refresh,
  Download,
  Monitor,
  Iphone,
  Tablet
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 组件导入
import DiamondIcon from './DiamondIcon.vue'

import type {
  DiamondPosition,
  DeviceType,
  DevicePreviewConfig
} from '@/types/diamond-position'
import { DEVICE_CONFIGS } from '@/types/diamond-position'

// Props
interface Props {
  visible: boolean
  items: DiamondPosition[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  items: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)
const activeDevice = ref<DeviceType>('mobile')
const isLandscape = ref(false)
const scale = ref(0.6)

// 设备配置
const devices = [
  { type: 'mobile' as DeviceType, name: '手机', icon: Iphone },
  { type: 'tablet' as DeviceType, name: '平板', icon: Tablet },
  { type: 'desktop' as DeviceType, name: '桌面', icon: Monitor }
]

// 计算属性
const enabledItems = computed(() => 
  props.items.filter(item => item.status === 1).slice(0, 20) // 最多显示20个
)

const currentConfig = computed(() => DEVICE_CONFIGS[activeDevice.value])

const emptySlots = computed(() => {
  const totalSlots = currentConfig.value.columns * 3 // 显示3行
  const filledSlots = enabledItems.value.length
  return Math.max(0, totalSlots - filledSlots)
})

const deviceFrameStyle = computed(() => {
  const config = currentConfig.value
  const width = isLandscape.value ? config.height : config.width
  const height = isLandscape.value ? config.width : config.height
  
  return {
    width: `${width * scale.value}px`,
    height: `${height * scale.value}px`,
    transform: `scale(${scale.value})`,
    transformOrigin: 'center center'
  }
})

const deviceScreenStyle = computed(() => {
  const config = currentConfig.value
  const width = isLandscape.value ? config.height : config.width
  const height = isLandscape.value ? config.width : config.height
  
  return {
    width: `${width}px`,
    height: `${height}px`
  }
})

const gridStyle = computed(() => ({
  gridTemplateColumns: `repeat(${currentConfig.value.columns}, 1fr)`,
  gap: `${currentConfig.value.gap}px`,
  padding: `${currentConfig.value.gap}px`
}))

const itemStyle = computed(() => ({
  minHeight: `${currentConfig.value.iconSize + 40}px`
}))

// 监听对话框显示状态
watch(() => props.visible, (newVisible) => {
  dialogVisible.value = newVisible
  if (newVisible) {
    resetPreview()
  }
})

watch(dialogVisible, (newVisible) => {
  emit('update:visible', newVisible)
})

// 方法
const handleDeviceChange = () => {
  isLandscape.value = false
  scale.value = activeDevice.value === 'desktop' ? 0.4 : 0.6
}

const resetPreview = () => {
  isLandscape.value = false
  scale.value = activeDevice.value === 'desktop' ? 0.4 : 0.6
}

const exportPreview = () => {
  ElMessage.info('导出截图功能开发中...')
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 600px;
}

.device-tabs {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-bottom: 1px solid #e4e7ed;
}

.preview-area {
  display: flex;
  gap: 40px;
  flex: 1;
  align-items: flex-start;
}

.device-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 40px;
}

.device-frame {
  position: relative;
  background: #000;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.device-frame.device-mobile {
  border-radius: 25px;
  padding: 15px;
}

.device-frame.device-tablet {
  border-radius: 20px;
  padding: 20px;
}

.device-frame.device-desktop {
  border-radius: 8px;
  padding: 8px;
  background: #333;
}

.device-frame.landscape {
  transform: rotate(0deg);
}

.device-screen {
  background: #fff;
  border-radius: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.device-mobile .device-screen {
  border-radius: 20px;
}

.device-tablet .device-screen {
  border-radius: 15px;
}

.device-desktop .device-screen {
  border-radius: 4px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #000;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
}

.status-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.app-header {
  background: #409eff;
  color: #fff;
  padding: 12px 16px;
  text-align: center;
}

.app-title {
  font-size: 16px;
  font-weight: 600;
}

.diamond-grid-preview {
  display: grid;
  flex: 1;
  background: #f8f9fa;
  overflow-y: auto;
}

.diamond-item-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.diamond-item-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.diamond-item-preview.empty-slot {
  background: #f5f7fa;
  border: 2px dashed #d9d9d9;
  color: #c0c4cc;
}

.item-icon-preview {
  margin-bottom: 8px;
}

.item-name-preview {
  font-size: 12px;
  color: #303133;
  text-align: center;
  line-height: 1.2;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.empty-icon {
  font-size: 24px;
  color: #c0c4cc;
}

.bottom-navigation {
  display: flex;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 8px 0;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  color: #909399;
  font-size: 10px;
  transition: color 0.3s ease;
}

.nav-item.active {
  color: #409eff;
}

.nav-item .el-icon {
  font-size: 20px;
}

.device-info {
  width: 300px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.device-info h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.control-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-top: 1px solid #e4e7ed;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-group label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  min-width: 60px;
}

.scale-value {
  color: #409eff;
  font-weight: 600;
  min-width: 40px;
}

.dialog-footer {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .preview-area {
    flex-direction: column;
    gap: 20px;
  }

  .device-info {
    width: 100%;
  }

  .control-panel {
    flex-wrap: wrap;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .device-container {
    padding: 20px;
  }

  .control-panel {
    flex-direction: column;
    gap: 16px;
  }

  .control-group {
    flex-direction: column;
    text-align: center;
  }
}

/* 对话框样式 */
:deep(.device-preview-dialog) {
  max-width: 1400px;
}

:deep(.device-preview-dialog .el-dialog__body) {
  padding: 20px;
}

/* 动画效果 */
.device-frame {
  animation: deviceAppear 0.5s ease-out;
}

@keyframes deviceAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.diamond-item-preview {
  animation: itemFadeIn 0.3s ease-out;
}

@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
