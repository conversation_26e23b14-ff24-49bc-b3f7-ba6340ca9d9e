<template>
  <div class="diamond-grid-container">
    <!-- 工具栏 -->
    <div class="grid-toolbar">
      <div class="toolbar-left">
        <el-button
          v-if="!sortMode"
          type="primary"
          @click="enableSortMode"
        >
          <el-icon><Sort /></el-icon>
          拖拽排序
        </el-button>
        <el-button
          v-else
          type="success"
          @click="disableSortMode"
        >
          <el-icon><Check /></el-icon>
          完成排序
        </el-button>
        
        <el-button
          v-if="selectedItems.length > 0"
          @click="selectAll"
        >
          <el-icon><Select /></el-icon>
          全选
        </el-button>
        <el-button
          v-if="selectedItems.length > 0"
          @click="clearSelection"
        >
          <el-icon><Close /></el-icon>
          取消选择
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <span class="selection-info">
          已选择 {{ selectedItems.length }} 项
        </span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 空状态 -->
    <div v-else-if="items.length === 0" class="empty-container">
      <el-empty description="暂无金刚位数据">
        <el-button type="primary" @click="$emit('create')">
          新增金刚位
        </el-button>
      </el-empty>
    </div>

    <!-- 网格内容 -->
    <div
      v-else
      class="diamond-grid"
      :class="{ 'sort-mode': sortMode, 'is-dragging': isDragging }"
    >
      <div
        v-for="(item, index) in items"
        :key="item.id"
        :data-id="item.id"
        class="diamond-item"
        :class="{
          'selected': isSelected(item),
          'disabled': item.status === 0,
          'draggable': sortMode && canDrag(item)
        }"
        :draggable="sortMode && canDrag(item)"
        @click="handleItemClick(item, $event)"
        @dragstart="handleDragStart($event, item, index)"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter($event, item, index)"
        @dragleave="handleDragLeave"
        @drop="handleDrop($event, item, index)"
        @dragend="handleDragEnd"
        @touchstart="handleTouchStart($event, item, index)"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <!-- 选择框 -->
        <div class="item-checkbox" @click.stop>
          <el-checkbox
            :model-value="isSelected(item)"
            @change="handleItemSelect(item, $event)"
          />
        </div>

        <!-- 拖拽手柄 -->
        <div v-if="sortMode" class="drag-handle">
          <el-icon><Rank /></el-icon>
        </div>

        <!-- 图标区域 -->
        <div class="item-icon">
          <DiamondIcon
            :icon="item.icon"
            :icon-type="item.iconType"
            :size="64"
            :alt="item.name"
          />
          
          <!-- 状态标识 -->
          <div v-if="item.status === 0" class="status-badge disabled">
            禁用
          </div>
        </div>

        <!-- 名称 -->
        <div class="item-name">{{ item.name }}</div>

        <!-- 操作按钮 -->
        <div class="item-actions" @click.stop>
          <el-button-group>
            <el-button
              size="small"
              @click="$emit('edit', item)"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              size="small"
              :type="item.status === 1 ? 'warning' : 'success'"
              @click="$emit('toggle-status', item, item.status === 1 ? 0 : 1)"
            >
              <el-icon v-if="item.status === 1"><Hide /></el-icon>
              <el-icon v-else><View /></el-icon>
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="$emit('delete', item)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 拖拽提示 -->
    <div v-if="sortMode" class="sort-tips">
      <el-alert
        title="拖拽排序模式"
        description="拖拽金刚位卡片可以调整显示顺序，完成后点击"完成排序"按钮保存"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Sort,
  Check,
  Select,
  Close,
  Edit,
  Delete,
  Hide,
  View,
  Rank
} from '@element-plus/icons-vue'

// 组件导入
import DiamondIcon from './DiamondIcon.vue'

// 组合式函数导入
import { useDragAndDrop, useTouchDragAndDrop } from '@/composables/useDragAndDrop'
import type { 
  DiamondPosition, 
  UpdateSortOrderRequest
} from '@/types/diamond-position'

// Props
interface Props {
  items: DiamondPosition[]
  loading: boolean
  selected: DiamondPosition[]
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  loading: false,
  selected: () => []
})

// Emits
const emit = defineEmits<{
  'update:selected': [value: DiamondPosition[]]
  'edit': [item: DiamondPosition]
  'delete': [item: DiamondPosition]
  'toggle-status': [item: DiamondPosition, status: 0 | 1]
  'sort-change': [newOrder: UpdateSortOrderRequest[]]
  'create': []
}>()

// 响应式数据
const sortMode = ref(false)
const selectedItems = ref<DiamondPosition[]>([])

// 拖拽功能
const {
  isDragging,
  handleDragStart,
  handleDragEnter,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  handleDragEnd
} = useDragAndDrop()

// 触摸拖拽功能
const {
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd
} = useTouchDragAndDrop()

// 计算属性
const isSelected = (item: DiamondPosition) => {
  return selectedItems.value.some(selected => selected.id === item.id)
}

const canDrag = (item: DiamondPosition) => {
  return item.status === 1 // 只有启用的金刚位可以拖拽
}

// 监听选中项变化
watch(() => props.selected, (newSelected) => {
  selectedItems.value = [...newSelected]
}, { immediate: true })

watch(selectedItems, (newSelected) => {
  emit('update:selected', newSelected)
}, { deep: true })

// 方法
const enableSortMode = () => {
  sortMode.value = true
  selectedItems.value = []
}

const disableSortMode = () => {
  sortMode.value = false
}

const selectAll = () => {
  selectedItems.value = [...props.items]
}

const clearSelection = () => {
  selectedItems.value = []
}

const handleItemClick = (item: DiamondPosition, event: MouseEvent) => {
  if (sortMode.value) return
  
  // 如果按住 Ctrl 或 Cmd 键，则多选
  if (event.ctrlKey || event.metaKey) {
    handleItemSelect(item, !isSelected(item))
  } else {
    // 单选
    selectedItems.value = [item]
  }
}

const handleItemSelect = (item: DiamondPosition, selected: boolean) => {
  if (selected) {
    if (!isSelected(item)) {
      selectedItems.value.push(item)
    }
  } else {
    selectedItems.value = selectedItems.value.filter(selected => selected.id !== item.id)
  }
}

// 拖拽事件处理
const handleDragStartWrapper = (event: DragEvent, item: DiamondPosition, index: number) => {
  if (!sortMode.value || !canDrag(item)) {
    event.preventDefault()
    return
  }
  handleDragStart(event, item, index)
}

const handleDragEnterWrapper = (event: DragEvent, item: DiamondPosition, index: number) => {
  if (!sortMode.value) return
  handleDragEnter(event, item, index)
}

const handleDropWrapper = (event: DragEvent, targetItem: DiamondPosition, targetIndex: number) => {
  if (!sortMode.value) return
  
  handleDrop(event, targetItem, targetIndex, props.items, (newOrder) => {
    emit('sort-change', newOrder)
  })
}

// 触摸事件处理
const handleTouchStartWrapper = (event: TouchEvent, item: DiamondPosition, index: number) => {
  if (!sortMode.value || !canDrag(item)) return
  handleTouchStart(event, item)
}

const handleTouchEndWrapper = (event: TouchEvent) => {
  if (!sortMode.value) return
  
  handleTouchEnd(event, props.items, (newOrder) => {
    emit('sort-change', newOrder)
  })
}
</script>

<style scoped>
.diamond-grid-container {
  padding: 20px;
}

.grid-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.selection-info {
  color: #606266;
  font-size: 14px;
}

.loading-container,
.empty-container {
  padding: 40px;
}

.diamond-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.diamond-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #fff;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.diamond-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.diamond-item.selected {
  border-color: #409eff;
  background: #ecf5ff;
}

.diamond-item.disabled {
  opacity: 0.6;
  background: #f5f7fa;
}

.diamond-item.draggable {
  cursor: move;
}

.diamond-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.diamond-item.drag-over {
  border-color: #67c23a;
  background: #f0f9ff;
}

.item-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

.drag-handle {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #909399;
  cursor: move;
}

.item-icon {
  position: relative;
  margin-bottom: 12px;
}

.status-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 10px;
  color: #fff;
}

.status-badge.disabled {
  background: #f56c6c;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  text-align: center;
  margin-bottom: 12px;
  line-height: 1.4;
  word-break: break-all;
}

.item-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.diamond-item:hover .item-actions {
  opacity: 1;
}

.sort-tips {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .diamond-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
  }

  .diamond-item {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .diamond-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .diamond-item {
    padding: 12px;
  }

  .item-name {
    font-size: 12px;
  }
}

/* 拖拽动画 */
.sort-mode .diamond-item {
  transition: transform 0.3s ease;
}

.is-dragging .diamond-item:not(.dragging) {
  transition: transform 0.3s ease;
}

/* 触摸设备优化 */
.touching {
  transform: scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}
</style>
