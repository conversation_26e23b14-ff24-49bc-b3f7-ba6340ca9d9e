<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择表情符号"
    width="600px"
    :close-on-click-modal="true"
  >
    <div class="emoji-picker-container">
      <!-- 搜索框 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索表情符号..."
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 分类标签 -->
      <div class="category-tabs">
        <el-button
          v-for="category in categories"
          :key="category.key"
          :type="activeCategory === category.key ? 'primary' : 'default'"
          size="small"
          @click="activeCategory = category.key"
        >
          {{ category.icon }} {{ category.name }}
        </el-button>
      </div>

      <!-- 表情符号网格 -->
      <div class="emoji-grid">
        <div
          v-for="emoji in filteredEmojis"
          :key="emoji.char"
          class="emoji-item"
          :title="emoji.name"
          @click="handleEmojiSelect(emoji.char)"
        >
          {{ emoji.char }}
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredEmojis.length === 0" class="empty-state">
        <el-empty description="没有找到匹配的表情符号" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'select': [emoji: string]
}>()

// 响应式数据
const dialogVisible = ref(false)
const searchKeyword = ref('')
const activeCategory = ref('smileys')

// 表情符号分类
const categories = [
  { key: 'smileys', name: '笑脸', icon: '😀' },
  { key: 'people', name: '人物', icon: '👤' },
  { key: 'animals', name: '动物', icon: '🐶' },
  { key: 'food', name: '食物', icon: '🍎' },
  { key: 'activities', name: '活动', icon: '⚽' },
  { key: 'travel', name: '旅行', icon: '🚗' },
  { key: 'objects', name: '物品', icon: '💡' },
  { key: 'symbols', name: '符号', icon: '❤️' },
  { key: 'flags', name: '旗帜', icon: '🏳️' }
]

// 表情符号数据
const emojiData = {
  smileys: [
    { char: '😀', name: '开心' },
    { char: '😃', name: '高兴' },
    { char: '😄', name: '大笑' },
    { char: '😁', name: '咧嘴笑' },
    { char: '😆', name: '笑哭' },
    { char: '😅', name: '苦笑' },
    { char: '🤣', name: '大笑' },
    { char: '😂', name: '笑哭' },
    { char: '🙂', name: '微笑' },
    { char: '🙃', name: '倒脸' },
    { char: '😉', name: '眨眼' },
    { char: '😊', name: '开心' },
    { char: '😇', name: '天使' },
    { char: '🥰', name: '爱心眼' },
    { char: '😍', name: '花痴' },
    { char: '🤩', name: '星星眼' },
    { char: '😘', name: '飞吻' },
    { char: '😗', name: '亲吻' },
    { char: '😚', name: '闭眼亲吻' },
    { char: '😙', name: '微笑亲吻' }
  ],
  people: [
    { char: '👶', name: '婴儿' },
    { char: '🧒', name: '儿童' },
    { char: '👦', name: '男孩' },
    { char: '👧', name: '女孩' },
    { char: '🧑', name: '成人' },
    { char: '👨', name: '男人' },
    { char: '👩', name: '女人' },
    { char: '🧓', name: '老人' },
    { char: '👴', name: '老爷爷' },
    { char: '👵', name: '老奶奶' },
    { char: '👤', name: '人影' },
    { char: '👥', name: '两人影' },
    { char: '👪', name: '家庭' },
    { char: '👫', name: '男女' },
    { char: '👬', name: '两男' },
    { char: '👭', name: '两女' }
  ],
  animals: [
    { char: '🐶', name: '狗' },
    { char: '🐱', name: '猫' },
    { char: '🐭', name: '鼠' },
    { char: '🐹', name: '仓鼠' },
    { char: '🐰', name: '兔子' },
    { char: '🦊', name: '狐狸' },
    { char: '🐻', name: '熊' },
    { char: '🐼', name: '熊猫' },
    { char: '🐨', name: '考拉' },
    { char: '🐯', name: '老虎' },
    { char: '🦁', name: '狮子' },
    { char: '🐮', name: '牛' },
    { char: '🐷', name: '猪' },
    { char: '🐸', name: '青蛙' },
    { char: '🐵', name: '猴子' },
    { char: '🐔', name: '鸡' }
  ],
  food: [
    { char: '🍎', name: '苹果' },
    { char: '🍊', name: '橙子' },
    { char: '🍋', name: '柠檬' },
    { char: '🍌', name: '香蕉' },
    { char: '🍉', name: '西瓜' },
    { char: '🍇', name: '葡萄' },
    { char: '🍓', name: '草莓' },
    { char: '🍈', name: '甜瓜' },
    { char: '🍒', name: '樱桃' },
    { char: '🍑', name: '桃子' },
    { char: '🥭', name: '芒果' },
    { char: '🍍', name: '菠萝' },
    { char: '🥥', name: '椰子' },
    { char: '🥝', name: '猕猴桃' },
    { char: '🍅', name: '番茄' },
    { char: '🍆', name: '茄子' }
  ],
  activities: [
    { char: '⚽', name: '足球' },
    { char: '🏀', name: '篮球' },
    { char: '🏈', name: '橄榄球' },
    { char: '⚾', name: '棒球' },
    { char: '🥎', name: '垒球' },
    { char: '🎾', name: '网球' },
    { char: '🏐', name: '排球' },
    { char: '🏉', name: '橄榄球' },
    { char: '🥏', name: '飞盘' },
    { char: '🎱', name: '台球' },
    { char: '🪀', name: '悠悠球' },
    { char: '🏓', name: '乒乓球' },
    { char: '🏸', name: '羽毛球' },
    { char: '🏒', name: '冰球' },
    { char: '🏑', name: '曲棍球' },
    { char: '🥍', name: '长曲棍球' }
  ],
  travel: [
    { char: '🚗', name: '汽车' },
    { char: '🚕', name: '出租车' },
    { char: '🚙', name: 'SUV' },
    { char: '🚌', name: '公交车' },
    { char: '🚎', name: '无轨电车' },
    { char: '🏎️', name: '赛车' },
    { char: '🚓', name: '警车' },
    { char: '🚑', name: '救护车' },
    { char: '🚒', name: '消防车' },
    { char: '🚐', name: '小巴' },
    { char: '🛻', name: '皮卡' },
    { char: '🚚', name: '卡车' },
    { char: '🚛', name: '货车' },
    { char: '🚜', name: '拖拉机' },
    { char: '🏍️', name: '摩托车' },
    { char: '🛵', name: '踏板车' }
  ],
  objects: [
    { char: '💡', name: '灯泡' },
    { char: '🔦', name: '手电筒' },
    { char: '🕯️', name: '蜡烛' },
    { char: '🪔', name: '油灯' },
    { char: '🔥', name: '火' },
    { char: '💰', name: '钱袋' },
    { char: '💳', name: '信用卡' },
    { char: '💎', name: '钻石' },
    { char: '⚖️', name: '天平' },
    { char: '🔧', name: '扳手' },
    { char: '🔨', name: '锤子' },
    { char: '⛏️', name: '镐' },
    { char: '🛠️', name: '工具' },
    { char: '⚙️', name: '齿轮' },
    { char: '🔩', name: '螺丝' },
    { char: '⚗️', name: '蒸馏器' }
  ],
  symbols: [
    { char: '❤️', name: '红心' },
    { char: '🧡', name: '橙心' },
    { char: '💛', name: '黄心' },
    { char: '💚', name: '绿心' },
    { char: '💙', name: '蓝心' },
    { char: '💜', name: '紫心' },
    { char: '🖤', name: '黑心' },
    { char: '🤍', name: '白心' },
    { char: '🤎', name: '棕心' },
    { char: '💔', name: '破碎的心' },
    { char: '❣️', name: '心形感叹号' },
    { char: '💕', name: '两颗心' },
    { char: '💞', name: '旋转的心' },
    { char: '💓', name: '跳动的心' },
    { char: '💗', name: '成长的心' },
    { char: '💖', name: '闪亮的心' }
  ],
  flags: [
    { char: '🏳️', name: '白旗' },
    { char: '🏴', name: '黑旗' },
    { char: '🏁', name: '方格旗' },
    { char: '🚩', name: '三角旗' },
    { char: '🏳️‍🌈', name: '彩虹旗' },
    { char: '🏳️‍⚧️', name: '跨性别旗' },
    { char: '🇨🇳', name: '中国' },
    { char: '🇺🇸', name: '美国' },
    { char: '🇬🇧', name: '英国' },
    { char: '🇯🇵', name: '日本' },
    { char: '🇰🇷', name: '韩国' },
    { char: '🇫🇷', name: '法国' },
    { char: '🇩🇪', name: '德国' },
    { char: '🇮🇹', name: '意大利' },
    { char: '🇪🇸', name: '西班牙' },
    { char: '🇷🇺', name: '俄罗斯' }
  ]
}

// 计算属性
const filteredEmojis = computed(() => {
  let emojis = emojiData[activeCategory.value] || []
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    emojis = emojis.filter(emoji => 
      emoji.name.toLowerCase().includes(keyword) ||
      emoji.char.includes(keyword)
    )
  }
  
  return emojis
})

// 监听对话框显示状态
watch(() => props.visible, (newVisible) => {
  dialogVisible.value = newVisible
  if (newVisible) {
    searchKeyword.value = ''
    activeCategory.value = 'smileys'
  }
})

watch(dialogVisible, (newVisible) => {
  emit('update:visible', newVisible)
})

// 方法
const handleEmojiSelect = (emoji: string) => {
  emit('select', emoji)
  handleClose()
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.emoji-picker-container {
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 16px;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 24px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.emoji-item:hover {
  background: #f5f7fa;
  transform: scale(1.2);
}

.empty-state {
  padding: 40px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

/* 滚动条样式 */
.emoji-grid::-webkit-scrollbar {
  width: 6px;
}

.emoji-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.emoji-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.emoji-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
