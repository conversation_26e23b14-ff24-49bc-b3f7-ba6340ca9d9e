import request from '@/utils/request'
import type {
  DiamondPosition,
  DiamondPositionQuery,
  CreateDiamondPositionRequest,
  UpdateDiamondPositionRequest,
  UpdateSortOrderRequest,
  TableDataInfo,
  ApiResponse,
  FileUploadParams,
  ImageUploadResponse,
  SvgUploadResponse
} from '@/types/diamond-position'

/**
 * 分页查询金刚位列表
 */
export function getDiamondPositionPage(params: DiamondPositionQuery) {
  return request<TableDataInfo<DiamondPosition>>({
    url: '/api/diamond-position/page',
    method: 'get',
    params
  })
}

/**
 * 获取所有启用的金刚位
 */
export function getDiamondPositionList() {
  return request<DiamondPosition[]>({
    url: '/api/diamond-position/list',
    method: 'get'
  })
}

/**
 * 获取金刚位详情
 */
export function getDiamondPosition(id: number) {
  return request<DiamondPosition>({
    url: `/api/diamond-position/${id}`,
    method: 'get'
  })
}

/**
 * 新增金刚位
 */
export function addDiamondPosition(data: CreateDiamondPositionRequest) {
  return request<ApiResponse>({
    url: '/api/diamond-position',
    method: 'post',
    data
  })
}

/**
 * 更新金刚位
 */
export function updateDiamondPosition(id: number, data: UpdateDiamondPositionRequest) {
  return request<ApiResponse>({
    url: `/api/diamond-position/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除金刚位
 */
export function deleteDiamondPosition(id: number) {
  return request<ApiResponse>({
    url: `/api/diamond-position/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除
 */
export function batchDeleteDiamondPosition(ids: number[]) {
  return request<ApiResponse>({
    url: '/api/diamond-position/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 更新排序
 */
export function updateDiamondPositionSort(data: UpdateSortOrderRequest[]) {
  return request<ApiResponse>({
    url: '/api/diamond-position/sort',
    method: 'put',
    data
  })
}

/**
 * 更新状态
 */
export function updateDiamondPositionStatus(id: number, status: number) {
  return request<ApiResponse>({
    url: `/api/diamond-position/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 批量更新状态
 */
export function batchUpdateDiamondPositionStatus(ids: number[], status: number) {
  return request<ApiResponse>({
    url: '/api/diamond-position/batch/status',
    method: 'patch',
    data: { ids, status }
  })
}

/**
 * 图片上传接口
 */
export function uploadImage(file: File, type: 'url' | 'base64' = 'url') {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)
  
  return request<ImageUploadResponse>({
    url: '/api/diamond-position/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * SVG文件上传接口
 */
export function uploadSvg(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request<SvgUploadResponse>({
    url: '/api/diamond-position/upload/svg',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 验证图标内容
 */
export function validateIcon(icon: string, iconType: string) {
  return request<{ valid: boolean; message: string }>({
    url: '/api/diamond-position/validate-icon',
    method: 'post',
    data: { icon, iconType }
  })
}

/**
 * 导出金刚位配置
 */
export function exportDiamondPosition(params: DiamondPositionQuery) {
  return request({
    url: '/api/diamond-position/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
