import { useUserStore } from '@/store/modules/user';

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export const checkPermi = (value: any) => {
  if (value && value instanceof Array && value.length > 0) {
    const permissions = useUserStore().permissions;
    const permissionDatas = value;
    const all_permission = '*:*:*';

    const hasPermission = permissions.some((permission) => {
      return all_permission === permission || permissionDatas.includes(permission);
    });

    if (!hasPermission) {
      return false;
    }
    return true;
  } else {
    console.error(`need roles! Like checkPermi="['system:user:add','system:user:edit']"`);
    return false;
  }
};

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export const checkRole = (value: any): boolean => {
  if (value && value instanceof Array && value.length > 0) {
    const roles = useUserStore().roles;
    const permissionRoles = value;
    const super_admin = 'admin';

    const hasRole = roles.some((role) => {
      return super_admin === role || permissionRoles.includes(role);
    });

    if (!hasRole) {
      return false;
    }
    return true;
  } else {
    console.error(`need roles! Like checkRole="['admin','editor']"`);
    return false;
  }
};

/**
 * 检查用户是否有指定权限
 * @param permission 权限标识
 * @returns 是否有权限
 */
export function hasPermission(permission: string): boolean {
  const userStore = useUserStore()
  const permissions = userStore.permissions || []

  // 超级管理员拥有所有权限
  if (permissions.includes('*:*:*')) {
    return true
  }

  return permissions.includes(permission)
}

/**
 * 金刚位相关权限常量
 */
export const DIAMOND_PERMISSIONS = {
  // 基础权限
  LIST: 'front:diamond:list',           // 查看列表
  DETAIL: 'front:diamond:detail',       // 查看详情

  // 操作权限
  ADD: 'front:diamond:add',             // 新增
  EDIT: 'front:diamond:edit',           // 编辑
  DELETE: 'front:diamond:delete',       // 删除

  // 批量操作权限
  BATCH_DELETE: 'front:diamond:batch:delete',     // 批量删除
  BATCH_STATUS: 'front:diamond:batch:status',     // 批量状态更新

  // 高级权限
  SORT: 'front:diamond:sort',           // 排序
  EXPORT: 'front:diamond:export',       // 导出
  UPLOAD: 'front:diamond:upload',       // 文件上传
  PREVIEW: 'front:diamond:preview'      // 多设备预览
} as const

/**
 * 权限组合检查
 */
export const DiamondPermissionChecker = {
  /**
   * 检查是否可以查看金刚位
   */
  canView(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.LIST)
  },

  /**
   * 检查是否可以新增金刚位
   */
  canAdd(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.ADD)
  },

  /**
   * 检查是否可以编辑金刚位
   */
  canEdit(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.EDIT)
  },

  /**
   * 检查是否可以删除金刚位
   */
  canDelete(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.DELETE)
  },

  /**
   * 检查是否可以批量操作
   */
  canBatchOperate(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.BATCH_DELETE) ||
           hasPermission(DIAMOND_PERMISSIONS.BATCH_STATUS)
  },

  /**
   * 检查是否可以排序
   */
  canSort(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.SORT)
  },

  /**
   * 检查是否可以导出
   */
  canExport(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.EXPORT)
  },

  /**
   * 检查是否可以上传文件
   */
  canUpload(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.UPLOAD)
  },

  /**
   * 检查是否可以预览
   */
  canPreview(): boolean {
    return hasPermission(DIAMOND_PERMISSIONS.PREVIEW)
  }
}
