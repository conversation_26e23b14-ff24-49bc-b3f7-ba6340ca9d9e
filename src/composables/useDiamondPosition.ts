import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  DiamondPosition,
  DiamondPositionQuery,
  CreateDiamondPositionRequest,
  UpdateDiamondPositionRequest,
  UpdateSortOrderRequest
} from '@/types/diamond-position'
import {
  getDiamondPositionPage,
  getDiamondPositionList,
  addDiamondPosition,
  updateDiamondPosition,
  deleteDiamondPosition,
  batchDeleteDiamondPosition,
  updateDiamondPositionSort,
  updateDiamondPositionStatus,
  batchUpdateDiamondPositionStatus
} from '@/api/diamond-position'

/**
 * 金刚位配置管理组合式函数
 */
export function useDiamondPosition() {
  // 响应式数据
  const loading = ref(false)
  const items = ref<DiamondPosition[]>([])
  const total = ref(0)
  
  // 查询参数
  const queryParams = reactive<DiamondPositionQuery>({
    pageNum: 1,
    pageSize: 10,
    name: '',
    iconType: undefined,
    status: undefined,
    orderBy: 'sortOrder',
    orderDirection: 'asc'
  })

  // 计算属性
  const enabledItems = computed(() => 
    items.value.filter(item => item.status === 1)
  )

  /**
   * 获取金刚位列表
   */
  const fetchItems = async () => {
    loading.value = true
    try {
      const response = await getDiamondPositionPage(queryParams)
      items.value = response.rows
      total.value = response.total
    } catch (error) {
      console.error('获取金刚位列表失败:', error)
      ElMessage.error('获取金刚位列表失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取所有启用的金刚位
   */
  const fetchEnabledItems = async () => {
    try {
      const response = await getDiamondPositionList()
      return response
    } catch (error) {
      console.error('获取启用金刚位列表失败:', error)
      ElMessage.error('获取启用金刚位列表失败')
      return []
    }
  }

  /**
   * 创建金刚位
   */
  const createItem = async (data: CreateDiamondPositionRequest) => {
    try {
      await addDiamondPosition(data)
      ElMessage.success('新增金刚位成功')
      await fetchItems()
    } catch (error) {
      console.error('新增金刚位失败:', error)
      ElMessage.error('新增金刚位失败')
      throw error
    }
  }

  /**
   * 更新金刚位
   */
  const updateItem = async (id: number, data: UpdateDiamondPositionRequest) => {
    try {
      await updateDiamondPosition(id, data)
      ElMessage.success('更新金刚位成功')
      await fetchItems()
    } catch (error) {
      console.error('更新金刚位失败:', error)
      ElMessage.error('更新金刚位失败')
      throw error
    }
  }

  /**
   * 删除金刚位
   */
  const deleteItem = async (id: number) => {
    try {
      await deleteDiamondPosition(id)
      ElMessage.success('删除金刚位成功')
      await fetchItems()
    } catch (error) {
      console.error('删除金刚位失败:', error)
      ElMessage.error('删除金刚位失败')
      throw error
    }
  }

  /**
   * 批量删除金刚位
   */
  const batchDelete = async (ids: number[]) => {
    try {
      await batchDeleteDiamondPosition(ids)
      ElMessage.success(`成功删除${ids.length}个金刚位`)
      await fetchItems()
    } catch (error) {
      console.error('批量删除金刚位失败:', error)
      ElMessage.error('批量删除金刚位失败')
      throw error
    }
  }

  /**
   * 更新排序
   */
  const updateSortOrder = async (sortData: UpdateSortOrderRequest[]) => {
    try {
      await updateDiamondPositionSort(sortData)
      ElMessage.success('更新排序成功')
      await fetchItems()
    } catch (error) {
      console.error('更新排序失败:', error)
      ElMessage.error('更新排序失败')
      throw error
    }
  }

  /**
   * 切换状态
   */
  const toggleStatus = async (id: number, status: number) => {
    try {
      await updateDiamondPositionStatus(id, status)
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}成功`)
      await fetchItems()
    } catch (error) {
      console.error('切换状态失败:', error)
      ElMessage.error('切换状态失败')
      throw error
    }
  }

  /**
   * 批量更新状态
   */
  const batchUpdateStatus = async (ids: number[], status: number) => {
    try {
      await batchUpdateDiamondPositionStatus(ids, status)
      ElMessage.success(`批量${status === 1 ? '启用' : '禁用'}成功`)
      await fetchItems()
    } catch (error) {
      console.error('批量更新状态失败:', error)
      ElMessage.error('批量更新状态失败')
      throw error
    }
  }

  /**
   * 搜索
   */
  const search = () => {
    queryParams.pageNum = 1
    fetchItems()
  }

  /**
   * 重置搜索
   */
  const resetSearch = () => {
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      name: '',
      iconType: undefined,
      status: undefined,
      orderBy: 'sortOrder',
      orderDirection: 'asc'
    })
    fetchItems()
  }

  return {
    // 响应式数据
    loading,
    items,
    total,
    queryParams,
    enabledItems,
    
    // 方法
    fetchItems,
    fetchEnabledItems,
    createItem,
    updateItem,
    deleteItem,
    batchDelete,
    updateSortOrder,
    toggleStatus,
    batchUpdateStatus,
    search,
    resetSearch
  }
}
