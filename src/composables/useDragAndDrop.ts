import { ref, nextTick } from 'vue'
import type { DiamondPosition, UpdateSortOrderRequest, DragSortEvent } from '@/types/diamond-position'

/**
 * 拖拽排序组合式函数
 */
export function useDragAndDrop() {
  const isDragging = ref(false)
  const draggedItem = ref<DiamondPosition | null>(null)
  const draggedIndex = ref(-1)
  const targetIndex = ref(-1)

  /**
   * 开始拖拽
   */
  const handleDragStart = (event: DragEvent, item: DiamondPosition, index: number) => {
    if (!event.dataTransfer) return

    isDragging.value = true
    draggedItem.value = item
    draggedIndex.value = index

    // 设置拖拽数据
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', item.id?.toString() || '')

    // 设置拖拽图像
    const dragImage = event.target as HTMLElement
    if (dragImage) {
      event.dataTransfer.setDragImage(dragImage, 0, 0)
    }

    // 添加拖拽样式
    nextTick(() => {
      const element = event.target as HTMLElement
      element.classList.add('dragging')
    })
  }

  /**
   * 拖拽进入
   */
  const handleDragEnter = (event: DragEvent, item: DiamondPosition, index: number) => {
    event.preventDefault()
    
    if (draggedItem.value && draggedItem.value.id !== item.id) {
      targetIndex.value = index
      
      // 添加拖拽目标样式
      const element = event.currentTarget as HTMLElement
      element.classList.add('drag-over')
    }
  }

  /**
   * 拖拽经过
   */
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault()
    event.dataTransfer!.dropEffect = 'move'
  }

  /**
   * 拖拽离开
   */
  const handleDragLeave = (event: DragEvent) => {
    const element = event.currentTarget as HTMLElement
    element.classList.remove('drag-over')
  }

  /**
   * 放置
   */
  const handleDrop = (
    event: DragEvent, 
    targetItem: DiamondPosition, 
    targetIdx: number,
    items: DiamondPosition[],
    onSortChange: (newOrder: UpdateSortOrderRequest[]) => void
  ) => {
    event.preventDefault()
    
    const element = event.currentTarget as HTMLElement
    element.classList.remove('drag-over')

    if (!draggedItem.value || draggedItem.value.id === targetItem.id) {
      return
    }

    // 计算新的排序
    const newItems = [...items]
    const draggedItemData = newItems.splice(draggedIndex.value, 1)[0]
    newItems.splice(targetIdx, 0, draggedItemData)

    // 生成排序数据
    const sortData: UpdateSortOrderRequest[] = newItems.map((item, index) => ({
      id: item.id!,
      sortOrder: index + 1
    }))

    // 触发排序变更事件
    onSortChange(sortData)
  }

  /**
   * 拖拽结束
   */
  const handleDragEnd = (event: DragEvent) => {
    isDragging.value = false
    draggedItem.value = null
    draggedIndex.value = -1
    targetIndex.value = -1

    // 移除拖拽样式
    const element = event.target as HTMLElement
    element.classList.remove('dragging')

    // 移除所有拖拽相关样式
    document.querySelectorAll('.drag-over').forEach(el => {
      el.classList.remove('drag-over')
    })
  }

  return {
    isDragging,
    draggedItem,
    draggedIndex,
    targetIndex,
    handleDragStart,
    handleDragEnter,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleDragEnd
  }
}

/**
 * 触摸设备拖拽排序组合式函数
 */
export function useTouchDragAndDrop() {
  const isTouching = ref(false)
  const touchStartY = ref(0)
  const touchCurrentY = ref(0)
  const touchedItem = ref<DiamondPosition | null>(null)

  /**
   * 触摸开始
   */
  const handleTouchStart = (event: TouchEvent, item: DiamondPosition) => {
    isTouching.value = true
    touchedItem.value = item
    touchStartY.value = event.touches[0].clientY
    touchCurrentY.value = event.touches[0].clientY

    // 添加触摸样式
    const element = event.currentTarget as HTMLElement
    element.classList.add('touching')
  }

  /**
   * 触摸移动
   */
  const handleTouchMove = (event: TouchEvent) => {
    if (!isTouching.value) return

    event.preventDefault()
    touchCurrentY.value = event.touches[0].clientY

    // 计算移动距离
    const deltaY = touchCurrentY.value - touchStartY.value
    
    // 添加移动效果
    const element = event.currentTarget as HTMLElement
    element.style.transform = `translateY(${deltaY}px)`
  }

  /**
   * 触摸结束
   */
  const handleTouchEnd = (
    event: TouchEvent,
    items: DiamondPosition[],
    onSortChange: (newOrder: UpdateSortOrderRequest[]) => void
  ) => {
    if (!isTouching.value || !touchedItem.value) return

    const element = event.currentTarget as HTMLElement
    element.classList.remove('touching')
    element.style.transform = ''

    // 计算移动距离
    const deltaY = touchCurrentY.value - touchStartY.value
    const threshold = 50 // 移动阈值

    if (Math.abs(deltaY) > threshold) {
      // 确定移动方向和目标位置
      const currentIndex = items.findIndex(item => item.id === touchedItem.value!.id)
      let targetIndex = currentIndex

      if (deltaY > 0) {
        // 向下移动
        targetIndex = Math.min(currentIndex + 1, items.length - 1)
      } else {
        // 向上移动
        targetIndex = Math.max(currentIndex - 1, 0)
      }

      if (targetIndex !== currentIndex) {
        // 重新排序
        const newItems = [...items]
        const movedItem = newItems.splice(currentIndex, 1)[0]
        newItems.splice(targetIndex, 0, movedItem)

        // 生成排序数据
        const sortData: UpdateSortOrderRequest[] = newItems.map((item, index) => ({
          id: item.id!,
          sortOrder: index + 1
        }))

        onSortChange(sortData)
      }
    }

    // 重置状态
    isTouching.value = false
    touchedItem.value = null
    touchStartY.value = 0
    touchCurrentY.value = 0
  }

  return {
    isTouching,
    touchedItem,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd
  }
}
