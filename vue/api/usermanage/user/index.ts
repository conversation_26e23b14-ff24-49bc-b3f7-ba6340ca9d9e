import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UserVO, UserForm, UserQuery } from '@/api/usermanage/user/types';

/**
 * 查询用户管理列表
 * @param query
 * @returns {*}
 */

export const listUser = (query?: UserQuery): AxiosPromise<UserVO[]> => {
  return request({
    url: '/usermanage/user/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询用户管理详细
 * @param userId
 */
export const getUser = (userId: string | number): AxiosPromise<UserVO> => {
  return request({
    url: '/usermanage/user/' + userId,
    method: 'get'
  });
};

/**
 * 新增用户管理
 * @param data
 */
export const addUser = (data: UserForm) => {
  return request({
    url: '/usermanage/user',
    method: 'post',
    data: data
  });
};

/**
 * 修改用户管理
 * @param data
 */
export const updateUser = (data: UserForm) => {
  return request({
    url: '/usermanage/user',
    method: 'put',
    data: data
  });
};

/**
 * 删除用户管理
 * @param userId
 */
export const delUser = (userId: string | number | Array<string | number>) => {
  return request({
    url: '/usermanage/user/' + userId,
    method: 'delete'
  });
};
