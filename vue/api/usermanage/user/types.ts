export interface UserVO {
  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 用户名
   */
  username: string;

  /**
   * 密码
   */
  password: string;

  /**
   * 手机号(加密存储)
   */
  phonenumber: string;

  /**
   * 手机号哈希(用于查询)
   */
  phoneHash: string;

  /**
   * 邮箱
   */
  email: string;

  /**
   * 真实姓名(加密存储)
   */
  realName: string;

  /**
   * 身份证号(AES加密存储)
   */
  idCard: string | number;

  /**
   * 身份证哈希(用于验证)
   */
  idCardHash: string | number;

  /**
   * 头像
   */
  avatar: string;

  /**
   * 性别：0-未知，1-男，2-女
   */
  gender: number;

  /**
   * 生日
   */
  birthDay: string;

  /**
   * 用户类型：1-学员，2-教练
   */
  userType: number;

  /**
   * 状态：0-禁用，1-正常
   */
  status: number;

  /**
   * 是否VIP：0-否，1-是
   */
  isVip: number;

  /**
   * VIP到期时间
   */
  vipExpireTime: string;

  /**
   * 注册来源
   */
  registerSource: string;

  /**
   * 最后登录时间
   */
  lastLoginTime: string;

  /**
   * 隐私协议版本
   */
  privacyAgreementVersion: string;

  /**
   * 数据使用同意状态：0-未同意，1-已同意，2-已撤回
   */
  dataConsentStatus: number;

  /**
   * 同意时间
   */
  consentTime: string;

  /**
   * 数据保留天数(默认3年)
   */
  dataRetentionDays: number;

  /**
   * 匿名化处理时间
   */
  anonymizationTime: string;

  /**
   * 实名认证状态：0-未认证，1-已认证
   */
  realNameVerified: number;

  /**
   * 认证时间
   */
  verifyTime: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 注册时的设备
   */
  registerDevice: string;

}

export interface UserForm extends BaseEntity {
  /**
   * 状态：0-禁用，1-正常
   */
  status?: number;

}

export interface UserQuery extends PageQuery {

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 用户名
   */
  username?: string;

  /**
   * 手机号(加密存储)
   */
  phonenumber?: string;

  /**
   * 性别：0-未知，1-男，2-女
   */
  gender?: number;

  /**
   * 用户类型：1-学员，2-教练
   */
  userType?: number;

  /**
   * 状态：0-禁用，1-正常
   */
  status?: number;

  /**
   * 是否VIP：0-否，1-是
   */
  isVip?: number;

  /**
   * VIP到期时间
   */
  vipExpireTime?: string;

  /**
   * 注册来源
   */
  registerSource?: string;

  /**
   * 隐私协议版本
   */
  privacyAgreementVersion?: string;

  /**
   * 实名认证状态：0-未认证，1-已认证
   */
  realNameVerified?: number;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 注册时的设备
   */
  registerDevice?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



