<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户名" prop="username">
              <el-input v-model="queryParams.username" placeholder="请输入用户名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号(加密存储)" prop="phonenumber">
              <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号(加密存储)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="性别：0-未知，1-男，2-女" prop="gender">
              <el-input v-model="queryParams.gender" placeholder="请输入性别：0-未知，1-男，2-女" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态：0-禁用，1-正常" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态：0-禁用，1-正常" clearable >
                <el-option v-for="dict in oto_user_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否VIP：0-否，1-是" prop="isVip">
              <el-input v-model="queryParams.isVip" placeholder="请输入是否VIP：0-否，1-是" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="VIP到期时间" prop="vipExpireTime">
              <el-date-picker clearable
                v-model="queryParams.vipExpireTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择VIP到期时间"
              />
            </el-form-item>
            <el-form-item label="注册来源" prop="registerSource">
              <el-input v-model="queryParams.registerSource" placeholder="请输入注册来源" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="隐私协议版本" prop="privacyAgreementVersion">
              <el-input v-model="queryParams.privacyAgreementVersion" placeholder="请输入隐私协议版本" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="实名认证状态：0-未认证，1-已认证" prop="realNameVerified">
              <el-input v-model="queryParams.realNameVerified" placeholder="请输入实名认证状态：0-未认证，1-已认证" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker clearable
                v-model="queryParams.createTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择创建时间"
              />
            </el-form-item>
            <el-form-item label="注册时的设备" prop="registerDevice">
              <el-input v-model="queryParams.registerDevice" placeholder="请输入注册时的设备" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['usermanage:user:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['usermanage:user:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['usermanage:user:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['usermanage:user:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="userList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="用户ID" align="center" prop="userId" v-if="true" />
        <el-table-column label="用户名" align="center" prop="username" />
        <el-table-column label="密码" align="center" prop="password" />
        <el-table-column label="手机号(加密存储)" align="center" prop="phonenumber" />
        <el-table-column label="手机号哈希(用于查询)" align="center" prop="phoneHash" />
        <el-table-column label="邮箱" align="center" prop="email" />
        <el-table-column label="真实姓名(加密存储)" align="center" prop="realName" />
        <el-table-column label="身份证号(AES加密存储)" align="center" prop="idCard" />
        <el-table-column label="身份证哈希(用于验证)" align="center" prop="idCardHash" />
        <el-table-column label="头像" align="center" prop="avatar" />
        <el-table-column label="性别：0-未知，1-男，2-女" align="center" prop="gender">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender"/>
          </template>
        </el-table-column>
        <el-table-column label="生日" align="center" prop="birthDay" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.birthDay, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="用户类型：1-学员，2-教练" align="center" prop="userType" />
        <el-table-column label="状态：0-禁用，1-正常" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="oto_user_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="是否VIP：0-否，1-是" align="center" prop="isVip" />
        <el-table-column label="VIP到期时间" align="center" prop="vipExpireTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.vipExpireTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="注册来源" align="center" prop="registerSource" />
        <el-table-column label="最后登录时间" align="center" prop="lastLoginTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lastLoginTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="隐私协议版本" align="center" prop="privacyAgreementVersion" />
        <el-table-column label="数据使用同意状态：0-未同意，1-已同意，2-已撤回" align="center" prop="dataConsentStatus" />
        <el-table-column label="同意时间" align="center" prop="consentTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.consentTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数据保留天数(默认3年)" align="center" prop="dataRetentionDays" />
        <el-table-column label="匿名化处理时间" align="center" prop="anonymizationTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.anonymizationTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实名认证状态：0-未认证，1-已认证" align="center" prop="realNameVerified">
          <template #default="scope">
            <dict-tag :options="oto_user_real_name_verified" :value="scope.row.realNameVerified"/>
          </template>
        </el-table-column>
        <el-table-column label="认证时间" align="center" prop="verifyTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.verifyTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="注册时的设备" align="center" prop="registerDevice" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['usermanage:user:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['usermanage:user:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改用户管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="userFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="状态：0-禁用，1-正常" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in oto_user_status"
              :key="dict.value"
              :value="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User" lang="ts">
import { listUser, getUser, delUser, addUser, updateUser } from '@/api/usermanage/user';
import { UserVO, UserQuery, UserForm } from '@/api/usermanage/user/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { oto_user_status } = toRefs<any>(proxy?.useDict('oto_user_status'));

const userList = ref<UserVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const userFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: UserForm = {
  status: undefined,
}
const data = reactive<PageData<UserForm, UserQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    username: undefined,
    phonenumber: undefined,
    gender: undefined,
    userType: undefined,
    status: undefined,
    isVip: undefined,
    vipExpireTime: undefined,
    registerSource: undefined,
    privacyAgreementVersion: undefined,
    realNameVerified: undefined,
    createTime: undefined,
    registerDevice: undefined,
    params: {
    }
  },
  rules: {
    status: [
      { required: true, message: "状态：0-禁用，1-正常不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listUser(queryParams.value);
  userList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  userFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: UserVO[]) => {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加用户管理";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: UserVO) => {
  reset();
  const _userId = row?.userId || ids.value[0]
  const res = await getUser(_userId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改用户管理";
}

/** 提交按钮 */
const submitForm = () => {
  userFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.userId) {
        await updateUser(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addUser(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: UserVO) => {
  const _userIds = row?.userId || ids.value;
  await proxy?.$modal.confirm('是否确认删除用户管理编号为"' + _userIds + '"的数据项？').finally(() => loading.value = false);
  await delUser(_userIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('usermanage/user/export', {
    ...queryParams.value
  }, `user_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
